import React from "react";
import { Col, <PERSON> } from "antd";
import OrganizationInformation from "./OrganizationInformation";

const CommonStarts = (props) => {
  const { Dashboard, pouchDatabase, databasePrefix } = props;





  return (
    <>
      {Dashboard && <Dashboard {...props} />}
      <Row
        // gutter={[5, 5]}
        style={{ marginTop: 5 }}
      >
        {/* <Col span={16}>
                    <Notes data={notesData} />
                </Col> */}
        <Col span={24}>
          <Col>
            <OrganizationInformation
              pouchDatabase={pouchDatabase}
              databasePrefix={databasePrefix}
            />
          </Col>
          {/* <Col style={{ marginTop: 10 }}>
                        {databasePrefix !== "mission_control_" && <SubscriptionDetails pouchDatabase={pouchDatabase} databasePrefix={databasePrefix} />}
                    </Col> */}
        </Col>
        {/* <Col span={4}>
                <Notes data={notesData} />
                </Col> */}
        {/* <Col span={10}>
                    <Logs data={logsData} />
                </Col> */}
      </Row>
    </>
  );
};

export default CommonStarts;
