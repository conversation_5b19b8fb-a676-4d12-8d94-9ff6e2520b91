{"identifier": "net.haclab.prosy", "productName": "Prosy Property Management System", "build": {"devUrl": "http://localhost:3008", "beforeDevCommand": "cross-env PORT=3008 yarn start", "beforeBuildCommand": "yarn build", "frontendDist": "../build", "distDir": "../build"}, "app": {"windows": [{"title": "Prosy | Property Management System", "fullscreen": false, "minHeight": 768, "minWidth": 1024, "maximized": true, "resizable": true}], "security": {"csp": "default-src * 'unsafe-inline' 'unsafe-eval' data: blob: filesystem:; script-src * 'unsafe-inline' 'unsafe-eval'; connect-src * 'unsafe-inline'; img-src * data: blob: 'unsafe-inline'; frame-src *; style-src * 'unsafe-inline'; worker-src blob:;"}}, "bundle": {"windows": {"nsis": {"installerHooks": "./windows/hooks.nsi"}}, "icon": ["../icons/prosy/png/128x128.png", "../icons/prosy/mac/icon.icns", "../icons/prosy/win/icon.ico"]}}