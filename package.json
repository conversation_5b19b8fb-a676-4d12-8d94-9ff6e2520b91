{"name": "prosy", "private": true, "main": "electron/prod.js", "homepage": "./", "author": {"name": "Haclab co ltd", "email": "<EMAIL>", "url": "https://www.haclab.net"}, "license": "MIT", "repository": "https://github.com/haclab-co/prosy-releases", "dependencies": {"@ant-design/charts": "^2.2.6", "@ant-design/icons": "^5.2.6", "@ant-design/plots": "^1.2.5", "@ant-design/pro-components": "^2.8.1", "@capacitor-community/electron": "^5.0.1", "@capacitor/android": "next", "@capacitor/core": "^5.7.4", "@capacitor/status-bar": "next", "@capacitor/text-zoom": "^5.0.7", "@google/generative-ai": "^0.24.1", "@hello-pangea/dnd": "^18.0.1", "@react-pdf/renderer": "^3.3.8", "@tabler/icons": "^2.37.0", "@tabler/icons-react": "^2.37.0", "@tanstack/match-sorter-utils": "^8.19.4", "@tanstack/react-table": "^8.21.3", "@tanstack/react-virtual": "^3.13.9", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "antd": "^5.21.0", "antd-table-saveas-excel": "^2.2.1", "axios": "^1.5.1", "canvas": "^3.1.0", "case-anything": "^3.1.0", "color-scheme-hook": "^3.7.2", "custom-electron-titlebar": "^4.2.8", "date-fns": "3.3.1", "electron-context-menu": "^3.6.1", "electron-log": "^4.4.6", "electron-updater": "^6.1.1", "emailjs-com": "^3.2.0", "fs": "^0.0.1-security", "js-base64": "^3.7.5", "js-export-excel": "^1.1.4", "jspdf": "^2.5.1", "jspdf-autotable": "^3.6.0", "less": "^4.2.0", "loanjs": "^1.1.2", "moment": "^2.30.1", "nodemailer": "^6.9.10", "number-to-words": "^1.2.4", "numeral": "^2.0.6", "papaparse": "^5.5.3", "particles-bg": "^2.5.5", "pdf-lib": "^1.17.1", "pouchdb": "^8.0.1", "pouchdb-adapter-leveldb": "^9.0.0", "pouchdb-browser": "^8.0.1", "pouchdb-find": "^8.0.1", "pouchdb-replication": "^9.0.0", "pouchdb-upsert": "^2.2.0", "puppeteer": "^22.12.1", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-csv": "^2.2.2", "react-detect-offline": "^2.4.5", "react-device-detect": "^2.2.3", "react-dom": "^18.2.0", "react-favicon": "^2.0.3", "react-helmet-async": "^2.0.5", "react-highlight-words": "^0.20.0", "react-icons": "^4.11.0", "react-json-to-table": "^0.1.7", "react-print-components": "^1.0.4", "react-responsive": "^9.0.2", "react-scripts": "5.0.1", "react-thermal-printer": "^0.18.1", "react-to-pdf": "^1.0.1", "react-to-print": "^2.15.1", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "tauri": "^0.15.0", "to-words": "^3.6.1", "web-vitals": "^2.1.4", "xlsx": "^0.18.5", "zustand": "^4.4.1"}, "scripts": {"res": "npx @capacitor/assets generate --iconBackgroundColor #151a4b --iconBackgroundColorDark #151a4b --splashBackgroundColor #151a4b --splashBackgroundColorDark #151a4b --androidProject android-apps/prosy --assetPath icons/prosy/png", "start": "react-scripts-less --max_old_space_size=8192 start", "build": "node scripts/optimize-build.js", "build:analyze": "cross-env ANALYZE_BUNDLE=true node scripts/optimize-build.js", "build:fast": "cross-env SKIP_PREFLIGHT_CHECK=true react-scripts-less --max_old_space_size=8192 build", "test": "react-scripts-less test", "eject": "react-scripts-less eject", "prepublishOnly": "yarn build", "release": "yarn build && electron-builder --windows --publish always", "release:fast": "yarn build:fast && electron-builder --windows --publish always", "electron-start": "concurrently \"yarn start\" \"electron electron/prod.js\"", "electron-dev": "yarn electron electron/dev.js", "electron-dev:debug": "cross-env ELECTRON_IS_DEV=true yarn electron electron/dev.js", "dist-linux": "electron-builder --linux --x64", "dist-win32": "yarn build && electron-builder --win --ia32", "dist-win64": "yarn build && electron-builder --win --x64", "dist-mac": "yarn build && electron-builder --mac", "dist-all": "yarn build && electron-builder --win --linux --mac", "clean": "rimraf build dist node_modules/.cache", "clean:full": "rimraf build dist node_modules", "performance:analyze": "node scripts/analyze-performance.js", "ionic:build": "npm run build", "ionic:serve": "npm run start", "tauri": "tauri", "tauri:build": "tauri build", "tauri:dev": "tauri dev", "tauri:android": "tauri android dev", "tauri:ios": "tauri ios dev", "init-android": "node scripts/init-android.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@capacitor/cli": "next", "@tauri-apps/api": "^2.3.0", "@tauri-apps/cli": "^2.3.1", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "electron": "^25.3.1", "electron-builder": "^24.4.0", "electron-devtools-installer": "^2.2.4", "electron-icon-maker": "^0.0.5", "electron-is-dev": "^2.0.0", "react-scripts-less": "^5.0.4", "wait-on": "^8.0.1"}, "description": "Property Management System", "build": {"copyright": "Copyright © 2019 Haclab CO. LTD", "directories": {"output": "dist", "buildResources": "icons"}, "win": {"icon": "icons/prosy/win/icon.ico", "executableName": "Prosy", "target": [{"target": "nsis", "arch": ["x64"]}]}, "nsis": {"artifactName": "${name}_${arch}_setup.${ext}", "uninstallDisplayName": "${name}", "oneClick": false, "installerIcon": "icons/prosy/win/icon.ico", "uninstallerIcon": "icons/prosy/win/icon.ico", "allowToChangeInstallationDirectory": true, "license": "LICENSE.md", "runAfterFinish": true, "perMachine": false, "createDesktopShortcut": true, "createStartMenuShortcut": true, "deleteAppDataOnUninstall": true}, "linux": {"icon": "icons/prosy/png/256x256.png"}, "mac": {"icon": "icons/prosy/mac/icon.icns", "category": "public.app-category.utilities"}, "files": ["build/**/*", "electron/**/*", "electron/icons/**/*", "icons/**/*"], "extraResources": [{"from": "electron/icons/prosy", "to": "icons/prosy", "filter": ["**/*"]}], "publish": [{"provider": "github", "private": true, "owner": "haclab-co", "repo": "prosy-releases", "token": "****************************************"}], "productName": "Prosy Property Management System", "appId": "net.haclab.prosy"}, "version": "0.0.49", "tauri": {"build": {"distDir": "build", "devPath": "http://localhost:3008", "beforeDevCommand": "cross-env PORT=3008 yarn start", "beforeBuildCommand": "yarn build"}}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}