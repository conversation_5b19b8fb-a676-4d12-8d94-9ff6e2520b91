import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Button,
  Progress,
  Alert,
  Space,
  Typography,
  Divider,
  List,
  Tag,
  Statistic,
  Row,
  Col,
  Modal,
  message,
  Tooltip,
  Tabs,
  Table,
  Badge,
  Spin
} from 'antd';
import {
  SyncOutlined,
  DatabaseOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  StopOutlined,
  ReloadOutlined,
  DiffOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  SearchOutlined
} from '@ant-design/icons';
import { Base64 } from 'js-base64';
// import ProgressiveSync from '../../../../../Database/ProgressiveSync.js';

// Temporary inline ProgressiveSync class to avoid import issues
class ProgressiveSync {
  constructor(pouchDatabase, databasePrefix) {
    this.pouchDatabase = pouchDatabase;
    this.databasePrefix = databasePrefix;
    this.syncState = {
      isRunning: false,
      currentStage: null,
      progress: 0,
      totalStages: 5,
      errors: [],
      startTime: null,
    };
    this.callbacks = {};
  }

  async startProgressiveSync(options = {}) {
    if (this.syncState.isRunning) return false;

    this.syncState.isRunning = true;
    this.syncState.startTime = Date.now();
    this.syncState.errors = [];

    const stages = [
      'Essential Data',
      'Configuration Data',
      'Core Module Data',
      'Transaction Data',
      'Extended Data'
    ];

    try {
      for (let i = 0; i < stages.length; i++) {
        this.syncState.currentStage = stages[i];
        this.syncState.progress = ((i + 1) / stages.length) * 100;

        if (this.callbacks.onProgress) {
          this.callbacks.onProgress(this.syncState);
        }

        // Simulate sync work
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      this.syncState.isRunning = false;
      this.syncState.currentStage = 'Complete';

      if (this.callbacks.onComplete) {
        this.callbacks.onComplete(this.syncState);
      }

      return true;
    } catch (error) {
      this.syncState.isRunning = false;
      this.syncState.errors.push(error.message);

      if (this.callbacks.onError) {
        this.callbacks.onError(error, this.syncState);
      }

      return false;
    }
  }

  cancelSync() {
    this.syncState.isRunning = false;
    this.syncState.currentStage = 'Cancelled';
    if (this.callbacks.onError) {
      this.callbacks.onError(new Error('Sync cancelled by user'), this.syncState);
    }
  }

  getSyncStatus() {
    return { ...this.syncState };
  }

  onProgress(callback) { this.callbacks.onProgress = callback; }
  onComplete(callback) { this.callbacks.onComplete = callback; }
  onError(callback) { this.callbacks.onError = callback; }
}

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

/**
 * Database Sync Settings Component
 * Provides manual control over database synchronization with progress tracking
 */
const DatabaseSync = ({ pouchDatabase, databasePrefix, userOrganization, modules }) => {
  const [syncStatus, setSyncStatus] = useState({
    isRunning: false,
    currentStage: null,
    progress: 0,
    totalStages: 0,
    errors: [],
    startTime: null,
    estimatedTimeRemaining: null
  });

  const [syncHistory, setSyncHistory] = useState([]);
  const progressiveSyncRef = useRef(null);

  // Data comparison state
  const [comparisonData, setComparisonData] = useState([]);
  const [comparisonLoading, setComparisonLoading] = useState(false);
  const [lastComparisonTime, setLastComparisonTime] = useState(null);

  // Individual sync state
  const [syncingCollections, setSyncingCollections] = useState(new Set());
  const [syncingAll, setSyncingAll] = useState(false);

  // Dexie-only documents state
  const [dexieOnlyDocs, setDexieOnlyDocs] = useState({});
  const [showDexieModal, setShowDexieModal] = useState(false);
  const [selectedCollection, setSelectedCollection] = useState(null);
  const [loadingDexieOnly, setLoadingDexieOnly] = useState(new Set());

  // Debug modules on component mount
  useEffect(() => {
    console.log('[DatabaseSync] Component props:', {
      hasModules: !!modules,
      modulesType: typeof modules,
      moduleKeys: modules ? Object.keys(modules) : [],
      hasPouchDatabase: !!pouchDatabase,
      databasePrefix,
      userOrganization: userOrganization?.name
    });

    if (modules) {
      console.log('[DatabaseSync] Available modules:', modules);
      Object.keys(modules).forEach(key => {
        const module = modules[key];
        console.log(`[DatabaseSync] Module ${key}:`, {
          hasCollection: !!module?.collection,
          collection: module?.collection,
          name: module?.name,
          title: module?.title
        });
      });
    }
  }, [modules, pouchDatabase, databasePrefix, userOrganization]);

  useEffect(() => {
    // Initialize Progressive Sync
    if (pouchDatabase && databasePrefix) {
      progressiveSyncRef.current = new ProgressiveSync(pouchDatabase, databasePrefix);

      // Set up callbacks
      progressiveSyncRef.current.onProgress((status) => {
        setSyncStatus(status);
      });

      progressiveSyncRef.current.onComplete((status) => {
        setSyncStatus(status);
        message.success('Database synchronization completed successfully!');
        addToSyncHistory('Full Sync', 'Completed', status.errors.length);
      });

      progressiveSyncRef.current.onError((error, status) => {
        setSyncStatus(status);
        message.error(`Sync failed: ${error.message}`);
        addToSyncHistory('Full Sync', 'Failed', status.errors.length);
      });
    }
  }, [pouchDatabase, databasePrefix]);

  /**
   * Add entry to sync history
   */
  const addToSyncHistory = (type, status, errorCount = 0) => {
    const entry = {
      id: Date.now(),
      type,
      status,
      timestamp: new Date().toLocaleString(),
      errorCount,
      duration: syncStatus.startTime ? Date.now() - syncStatus.startTime : 0
    };

    setSyncHistory(prev => [entry, ...prev.slice(0, 9)]); // Keep last 10 entries
  };

  /**
   * Sync individual collection
   */
  const handleCollectionSync = async (collectionName) => {
    if (!pouchDatabase || !databasePrefix) {
      message.error('Database configuration not available');
      return;
    }

    // Add to syncing collections
    setSyncingCollections(prev => new Set([...prev, collectionName]));

    try {
      console.log(`[DatabaseSync] Starting sync for collection: ${collectionName}`);

      // Get database instance for the collection
      const db = pouchDatabase(collectionName, databasePrefix);
      if (!db) {
        throw new Error(`Collection ${collectionName} not available`);
      }

      // Perform sync operations
      const syncPromises = [];

      // Use SyncOrchestrator for coordinated sync (preferred method)
      if (db.syncOrchestrator) {
        syncPromises.push(
          db.syncOrchestrator.forceFullSync().catch(error => {
            console.warn(`[DatabaseSync] Orchestrator sync failed for ${collectionName}:`, error);
            return { error: error.message, type: 'Orchestrator' };
          })
        );
      } else {
        // Fallback to legacy methods (deprecated)
        // LAN sync (if available)
        if (db.lanDbSync) {
          syncPromises.push(
            db.lanDbSync().catch(error => {
              console.warn(`[DatabaseSync] LAN sync failed for ${collectionName}:`, error);
              return { error: error.message, type: 'LAN' };
            })
          );
        }

        // Remote sync (if available)
        if (db.dbSync) {
          syncPromises.push(
            db.dbSync().catch(error => {
              console.warn(`[DatabaseSync] Remote sync failed for ${collectionName}:`, error);
              return { error: error.message, type: 'Remote' };
            })
          );
        }
      }

      if (syncPromises.length === 0) {
        throw new Error('No sync methods available for this collection');
      }

      // Wait for all sync operations
      const results = await Promise.allSettled(syncPromises);

      // Check for errors
      const errors = results
        .filter(result => result.status === 'fulfilled' && result.value?.error)
        .map(result => result.value);

      if (errors.length > 0) {
        const errorMsg = errors.map(e => `${e.type}: ${e.error}`).join(', ');
        message.warning(`Sync completed with warnings for ${collectionName}: ${errorMsg}`);
      } else {
        message.success(`Successfully synced collection: ${collectionName}`);
      }

      // Refresh comparison data to show updated counts
      setTimeout(() => {
        runDataComparison();
      }, 1000);

    } catch (error) {
      console.error(`[DatabaseSync] Error syncing collection ${collectionName}:`, error);
      message.error(`Failed to sync ${collectionName}: ${error.message}`);
    } finally {
      // Remove from syncing collections
      setSyncingCollections(prev => {
        const newSet = new Set(prev);
        newSet.delete(collectionName);
        return newSet;
      });
    }
  };

  /**
   * Sync all collections from comparison data
   */
  const handleSyncAll = async () => {
    if (comparisonData.length === 0) {
      message.warning('No comparison data available. Please run comparison first.');
      return;
    }

    setSyncingAll(true);

    try {
      console.log('[DatabaseSync] Starting sync for all collections');

      // Get collections that are out of sync or have errors
      const collectionsToSync = comparisonData
        .filter(item => item.syncStatus !== 'synced')
        .map(item => item.collection);

      if (collectionsToSync.length === 0) {
        message.info('All collections are already in sync');
        setSyncingAll(false);
        return;
      }

      message.info(`Starting sync for ${collectionsToSync.length} collections...`);

      // Sync collections in batches to prevent overwhelming the system
      const batchSize = 3;
      let successCount = 0;
      let errorCount = 0;

      for (let i = 0; i < collectionsToSync.length; i += batchSize) {
        const batch = collectionsToSync.slice(i, i + batchSize);

        const batchPromises = batch.map(async (collection) => {
          try {
            // Sync collection directly without using handleCollectionSync to avoid state conflicts
            const db = pouchDatabase(collection, databasePrefix);
            if (!db) {
              throw new Error(`Collection ${collection} not available`);
            }

            const syncPromises = [];

            // Use SyncOrchestrator for coordinated sync (preferred method)
            if (db.syncOrchestrator) {
              syncPromises.push(
                db.syncOrchestrator.forceFullSync().catch(error => {
                  console.warn(`[DatabaseSync] Orchestrator sync failed for ${collection}:`, error);
                  return { error: error.message, type: 'Orchestrator' };
                })
              );
            } else {
              // Fallback to legacy methods (deprecated)
              // LAN sync (if available)
              if (db.lanDbSync) {
                syncPromises.push(
                  db.lanDbSync().catch(error => {
                    console.warn(`[DatabaseSync] LAN sync failed for ${collection}:`, error);
                    return { error: error.message, type: 'LAN' };
                  })
                );
              }

              // Remote sync (if available)
              if (db.dbSync) {
                syncPromises.push(
                  db.dbSync().catch(error => {
                    console.warn(`[DatabaseSync] Remote sync failed for ${collection}:`, error);
                    return { error: error.message, type: 'Remote' };
                  })
                );
              }
            }

            if (syncPromises.length > 0) {
              await Promise.allSettled(syncPromises);
            }

            successCount++;
          } catch (error) {
            console.error(`[DatabaseSync] Batch sync failed for ${collection}:`, error);
            errorCount++;
          }
        });

        await Promise.allSettled(batchPromises);

        // Small delay between batches
        if (i + batchSize < collectionsToSync.length) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      if (errorCount === 0) {
        message.success(`Successfully synced all ${successCount} collections`);
      } else {
        message.warning(`Sync completed: ${successCount} successful, ${errorCount} failed`);
      }

    } catch (error) {
      console.error('[DatabaseSync] Error in sync all:', error);
      message.error(`Failed to sync all collections: ${error.message}`);
    } finally {
      setSyncingAll(false);
      // Refresh comparison data
      setTimeout(() => {
        runDataComparison();
      }, 2000);
    }
  };

  /**
   * Cancel ongoing sync
   */
  const handleCancelSync = () => {
    if (progressiveSyncRef.current) {
      progressiveSyncRef.current.cancelSync();
      message.info('Database sync cancelled');
      addToSyncHistory('Full Sync', 'Cancelled', syncStatus.errors.length);
    }
  };

  /**
   * Find documents that exist in Dexie but not in LAN
   */
  const handleFindDexieOnlyDocs = async (collectionName) => {
    setLoadingDexieOnly(prev => new Set([...prev, collectionName]));

    try {
      console.log(`[DatabaseSync] Finding Dexie-only documents for ${collectionName}`);
      const result = await getDexieOnlyDocuments(collectionName);

      if (result.error) {
        message.error(`Failed to find Dexie-only documents for ${collectionName}: ${result.error}`);
        return;
      }

      setDexieOnlyDocs(prev => ({
        ...prev,
        [collectionName]: result.documents
      }));

      setSelectedCollection(collectionName);
      setShowDexieModal(true);

      if (result.documents.length > 0) {
        message.success(`Found ${result.documents.length} documents in Dexie but not in LAN for ${collectionName}`);
      } else {
        message.info(`No documents found in Dexie but not in LAN for ${collectionName}`);
      }

    } catch (error) {
      console.error(`[DatabaseSync] Error finding Dexie-only documents for ${collectionName}:`, error);
      message.error(`Failed to find Dexie-only documents: ${error.message}`);
    } finally {
      setLoadingDexieOnly(prev => {
        const newSet = new Set(prev);
        newSet.delete(collectionName);
        return newSet;
      });
    }
  };

  /**
   * Format duration in human readable format
   */
  const formatDuration = (ms) => {
    if (!ms) return 'N/A';
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  };

  /**
   * Get status color based on sync state
   */
  const getStatusColor = (status) => {
    switch (status) {
      case 'Completed': return 'success';
      case 'Failed': return 'error';
      case 'Cancelled': return 'warning';
      case 'Running': return 'processing';
      default: return 'default';
    }
  };

  /**
   * Get collections from modules prop - MUCH MORE ACCURATE!
   * This uses the actual modules defined in the app, ensuring we only compare real collections
   */
  const getCollectionsFromModules = () => {
    console.log('[DataComparison] Getting collections from modules prop...');
    const collections = [];

    if (modules && typeof modules === 'object') {
      // Extract collections from modules
      Object.keys(modules).forEach(moduleKey => {
        const module = modules[moduleKey];
        if (module && module.collection) {
          if (!collections.includes(module.collection)) {
            collections.push(module.collection);
            console.log(`[DataComparison] Found module collection: ${module.collection} (from ${moduleKey})`);
          }
        }
      });
    }

    // Always include essential collections that might not be in modules
    const essentialCollections = ['organizations', 'users', 'settings'];
    essentialCollections.forEach(collection => {
      if (!collections.includes(collection)) {
        collections.push(collection);
        console.log(`[DataComparison] Added essential collection: ${collection}`);
      }
    });

    console.log(`[DataComparison] Total collections from modules: ${collections.length}`, collections);
    return collections;
  };

  /**
   * Verify collections have data (optional verification step)
   */
  const verifyCollectionsHaveData = async (collections) => {
    console.log('[DataComparison] Verifying collections have data...');
    const collectionsWithData = [];

    for (const collectionName of collections) {
      try {
        if (pouchDatabase && typeof pouchDatabase === 'function') {
          const testDb = pouchDatabase(collectionName, databasePrefix);

          // Quick check if collection has actual data (excluding design docs)
          let hasData = false;
          try {
            if (testDb.pouchDb) {
              // Quick check using allDocs to exclude design documents
              const result = await testDb.pouchDb.allDocs({
                include_docs: false,
                startkey: '',
                endkey: '_design/',
                limit: 1 // Just check if any data exists
              });

              const actualDataDocs = result.rows.filter(row =>
                !row.id.startsWith('_design/') &&
                !row.id.startsWith('_local/') &&
                !row.id.startsWith('_')
              );

              hasData = actualDataDocs.length > 0;
            } else if (testDb.db) {
              const result = await testDb.db.allDocs({
                include_docs: false,
                startkey: '',
                endkey: '_design/',
                limit: 1
              });

              const actualDataDocs = result.rows.filter(row =>
                !row.id.startsWith('_design/') &&
                !row.id.startsWith('_local/') &&
                !row.id.startsWith('_')
              );

              hasData = actualDataDocs.length > 0;
            }
          } catch (error) {
            // Collection might not exist, but we'll still include it for comparison
            console.warn(`[DataComparison] Could not verify data for ${collectionName}:`, error.message);
            hasData = true; // Include it anyway for comparison
          }

          if (hasData) {
            collectionsWithData.push(collectionName);
            console.log(`[DataComparison] Verified collection has data: ${collectionName}`);
          } else {
            console.log(`[DataComparison] Collection appears empty: ${collectionName}`);
            // Still include empty collections for comparison
            collectionsWithData.push(collectionName);
          }
        }
      } catch (error) {
        console.warn(`[DataComparison] Error verifying collection ${collectionName}:`, error.message);
        // Include it anyway for comparison
        collectionsWithData.push(collectionName);
      }
    }

    console.log(`[DataComparison] Verified ${collectionsWithData.length} collections:`, collectionsWithData);
    return collectionsWithData;
  };

  /**
   * Get collections to compare (fallback method)
   */
  const getAllDatabaseCollections = () => {
    // Fallback to basic collections if modules prop is not available
    const basicCollections = [
      'organizations', 'users', 'settings', 'customers', 'products', 'invoices'
    ];

    console.log(`[DataComparison] Using fallback collections (modules prop not available):`, basicCollections);
    return basicCollections;
  };

  /**
   * Compare data counts across all database sources for a single collection
   * ENHANCED: Now includes LAN and Remote CouchDB counts with connectivity testing
   */
  const compareCollectionData = async (collectionName) => {
    console.log(`[DataComparison] Starting comprehensive comparison for collection: ${collectionName}`);

    try {
      let dexieCount = 0;
      let pouchCount = 0;
      let lanCount = 0;
      let remoteCount = 0;
      let dexieError = null;
      let pouchError = null;
      let lanError = null;
      let remoteError = null;
      let lastSyncTime = null;
      let lanAvailable = false;
      let remoteAvailable = false;

      // ENHANCED: Create database instance using the factory function
      let dbInstance = null;
      try {
        if (pouchDatabase && typeof pouchDatabase === 'function') {
          dbInstance = pouchDatabase(collectionName, databasePrefix);
          console.log(`[DataComparison] Created database instance for ${collectionName}`);
        } else {
          throw new Error('pouchDatabase factory function not available');
        }
      } catch (error) {
        console.error(`[DataComparison] Failed to create database instance for ${collectionName}:`, error);
        return {
          collection: collectionName,
          dexieCount: 0,
          pouchCount: 0,
          lanCount: 0,
          remoteCount: 0,
          difference: 0,
          syncStatus: 'error',
          dexieError: `Database factory error: ${error.message}`,
          pouchError: null,
          lanError: null,
          remoteError: null,
          lastSyncTime: null,
          lanAvailable: false,
          remoteAvailable: false
        };
      }

      // ENHANCED: Try to access Dexie data directly from IndexedDB
      try {
        console.log(`[DataComparison] Attempting to access Dexie data for ${collectionName}`);
        const dexieResult = await getDexieDocumentCount(collectionName);
        dexieCount = dexieResult.count;
        dexieError = dexieResult.error;

        if (dexieCount > 0) {
          console.log(`[DataComparison] Found ${dexieCount} documents in Dexie for ${collectionName}`);
        } else if (dexieError) {
          console.warn(`[DataComparison] Dexie access error for ${collectionName}:`, dexieError);
        } else {
          console.log(`[DataComparison] No Dexie data found for ${collectionName}`);
        }
      } catch (error) {
        dexieError = error.message;
        console.warn(`[DataComparison] Dexie error for ${collectionName}:`, error);
      }

      // PouchDB comparison removed - focusing on Dexie vs LAN comparison
      try {
        console.log(`[DataComparison] Skipping PouchDB count for ${collectionName} - focusing on Dexie vs LAN comparison`);
        pouchCount = 0;
        pouchError = 'PouchDB comparison disabled';
      } catch (error) {
        pouchError = error.message;
        console.warn(`[DataComparison] PouchDB error for ${collectionName}:`, error);
      }

      // ENHANCED: Test connectivity and get LAN CouchDB count
      try {
        lanAvailable = await testLanConnectivity();
        if (lanAvailable) {
          const lanResult = await getLanDocumentCount(collectionName);
          lanCount = lanResult.count;
          lanError = lanResult.error;
          console.log(`[DataComparison] LAN count for ${collectionName}: ${lanCount}${lanError ? ` (error: ${lanError})` : ''}`);
        } else {
          lanError = 'LAN server not reachable';
          console.log(`[DataComparison] LAN not available for ${collectionName}`);
        }
      } catch (error) {
        lanError = error.message;
        console.warn(`[DataComparison] LAN connectivity/count error for ${collectionName}:`, error);
      }

      // Remote CouchDB comparison removed - focusing on Dexie vs LAN comparison
      try {
        console.log(`[DataComparison] Skipping Remote CouchDB count for ${collectionName} - focusing on Dexie vs LAN comparison`);
        remoteAvailable = false;
        remoteCount = 0;
        remoteError = 'Remote CouchDB comparison disabled';
      } catch (error) {
        remoteError = error.message;
        console.warn(`[DataComparison] Remote error for ${collectionName}:`, error);
      }

      // ENHANCED: Use database's built-in sync stats if available
      try {
        if (dbInstance && typeof dbInstance.getSyncStats === 'function') {
          const syncStats = await dbInstance.getSyncStats();
          if (syncStats) {
            dexieCount = syncStats.dexie?.total || dexieCount;
            pouchCount = syncStats.pouchdb?.total || pouchCount;
            console.log(`[DataComparison] Using sync stats for ${collectionName} - Dexie: ${dexieCount}, PouchDB: ${pouchCount}`);
          }
        }
      } catch (error) {
        console.warn(`[DataComparison] Could not get sync stats for ${collectionName}:`, error);
      }

      // ENHANCED: Calculate sync status focusing on Dexie vs LAN comparison
      const allCounts = [dexieCount];
      if (lanAvailable && !lanError) allCounts.push(lanCount);

      const maxCount = Math.max(...allCounts);
      const minCount = Math.min(...allCounts);
      const totalDifference = maxCount - minCount;

      let syncStatus = 'synced';
      if (dexieError || (lanAvailable && lanError)) {
        syncStatus = 'error';
      } else if (totalDifference > 0) {
        syncStatus = 'out-of-sync';
      }

      const result = {
        collection: collectionName,
        dexieCount,
        pouchCount,
        lanCount,
        remoteCount,
        difference: totalDifference,
        syncStatus,
        dexieError,
        pouchError,
        lanError,
        remoteError,
        lastSyncTime,
        lanAvailable,
        remoteAvailable
      };

      console.log(`[DataComparison] Comprehensive comparison result for ${collectionName}:`, result);
      return result;

    } catch (error) {
      console.error(`[DataComparison] Error comparing ${collectionName}:`, error);
      return {
        collection: collectionName,
        dexieCount: 0,
        pouchCount: 0,
        lanCount: 0,
        remoteCount: 0,
        difference: 0,
        syncStatus: 'error',
        dexieError: error.message,
        pouchError: null,
        lanError: null,
        remoteError: null,
        lastSyncTime: null,
        lanAvailable: false,
        remoteAvailable: false
      };
    }
  };

  /**
   * Test LAN CouchDB connectivity
   */
  const testLanConnectivity = async () => {
    try {
      // Get LAN credentials from localStorage
      const lanCredentialsHash = localStorage.getItem('LAN_CREDENTIALS');
      if (!lanCredentialsHash) {
        console.log('[DataComparison] No LAN credentials found in localStorage');
        return false;
      }

      // Decrypt LAN credentials
      const decryptPassword = (password) =>
        Base64.decode(
          Base64.decode(Base64.decode(Base64.decode(Base64.decode(password))))
        );

      const lanCredentials = JSON.parse(decryptPassword(lanCredentialsHash));
      const { host, port, username, password } = lanCredentials;

      console.log(`[DataComparison] LAN credentials check:`, {
        hasHost: !!host,
        hasPort: !!port,
        hasUsername: !!username,
        hasPassword: !!password,
        host: host || 'missing',
        port: port || 'missing'
      });

      if (!host || !port || !username || !password) {
        console.log('[DataComparison] Incomplete LAN credentials');
        return false;
      }

      // Test LAN server connectivity
      const rootUrl = `http://${host}:${port}`;
      console.log(`[DataComparison] Testing LAN connectivity to: ${rootUrl}`);

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 8000); // Increased timeout

      // Create authorization header
      const authHeader = 'Basic ' + btoa(`${username}:${password}`);

      const response = await fetch(rootUrl, {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Accept': 'application/json',
          'Authorization': authHeader
        }
      });

      clearTimeout(timeoutId);
      console.log(`[DataComparison] LAN connectivity test result: ${response.ok} (status: ${response.status})`);
      return response.ok;
    } catch (error) {
      console.warn('[DataComparison] LAN connectivity test failed:', error.message);
      // Return true anyway to allow the count attempt - sometimes connectivity test fails but actual data access works
      return true;
    }
  };

  /**
   * Test Remote CouchDB connectivity
   */
  const testRemoteConnectivity = async () => {
    try {
      // Get database prefix from localStorage
      const databasePrefix = localStorage.getItem('LOCAL_STORAGE_DATABASE_PREFIX');
      if (!databasePrefix) {
        console.log('[DataComparison] No database prefix found in localStorage');
        return false;
      }

      // Use the correct remote connection string from constants
      const REMOTE_CONNECTION = 'https://therick:<EMAIL>/';
      const rootUrl = REMOTE_CONNECTION.replace(/\/$/, '');

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      const response = await fetch(rootUrl, {
        method: 'GET',
        signal: controller.signal,
        headers: { 'Accept': 'application/json' }
      });

      clearTimeout(timeoutId);
      console.log(`[DataComparison] Remote connectivity test result: ${response.ok}`);
      return response.ok;
    } catch (error) {
      console.warn('[DataComparison] Remote connectivity test failed:', error.message);
      return false;
    }
  };

  /**
   * Get document count from LAN CouchDB
   */
  const getLanDocumentCount = async (collectionName) => {
    try {
      // Get LAN credentials and database prefix
      const lanCredentialsHash = localStorage.getItem('LAN_CREDENTIALS');
      const databasePrefix = localStorage.getItem('LOCAL_STORAGE_DATABASE_PREFIX');

      if (!lanCredentialsHash || !databasePrefix) {
        return { count: 0, error: 'LAN credentials or database prefix not configured' };
      }

      // Decrypt LAN credentials
      const decryptPassword = (password) =>
        Base64.decode(
          Base64.decode(Base64.decode(Base64.decode(Base64.decode(password))))
        );

      const lanCredentials = JSON.parse(decryptPassword(lanCredentialsHash));
      const { host, port, username, password } = lanCredentials;

      if (!host || !port || !username || !password) {
        return { count: 0, error: 'Incomplete LAN credentials' };
      }

      // Build database URL without credentials
      const dbUrl = `http://${host}:${port}/${databasePrefix}${collectionName}`;
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 8000);

      // Create authorization header
      const authHeader = 'Basic ' + btoa(`${username}:${password}`);

      // First try to get database info
      const response = await fetch(dbUrl, {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Accept': 'application/json',
          'Authorization': authHeader
        }
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        if (response.status === 404) {
          return { count: 0, error: null }; // Database doesn't exist, which is valid
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const dbInfo = await response.json();

      // Get actual document count excluding design documents
      const allDocsUrl = `${dbUrl}/_all_docs?limit=0&startkey=""&endkey="_design/"`;
      const docsController = new AbortController();
      const docsTimeoutId = setTimeout(() => docsController.abort(), 8000);

      const docsResponse = await fetch(allDocsUrl, {
        method: 'GET',
        signal: docsController.signal,
        headers: {
          'Accept': 'application/json',
          'Authorization': authHeader
        }
      });

      clearTimeout(docsTimeoutId);

      if (docsResponse.ok) {
        const docsResult = await docsResponse.json();
        return { count: docsResult.total_rows || 0, error: null };
      } else {
        // Fallback to database info doc_count (may include design docs)
        const actualCount = Math.max(0, (dbInfo.doc_count || 0) - (dbInfo.doc_del_count || 0));
        return { count: actualCount, error: null };
      }

    } catch (error) {
      console.warn(`[DataComparison] LAN count error for ${collectionName}:`, error.message);
      return { count: 0, error: error.message };
    }
  };

  /**
   * Get document count from Dexie IndexedDB
   */
  const getDexieDocumentCount = async (collectionName) => {
    try {
      // Get database prefix for Dexie database naming
      const databasePrefix = localStorage.getItem('LOCAL_STORAGE_DATABASE_PREFIX') || '';
      const dexieDbName = `${databasePrefix}${collectionName}`;

      console.log(`[DataComparison] Attempting to access Dexie database: ${dexieDbName}`);

      // Try to access IndexedDB directly to find Dexie databases
      const databases = await indexedDB.databases();
      const dexieDb = databases.find(db =>
        db.name === dexieDbName ||
        db.name === collectionName ||
        db.name.includes(collectionName)
      );

      if (!dexieDb) {
        console.log(`[DataComparison] No Dexie database found for ${collectionName}`);
        return { count: 0, error: null };
      }

      console.log(`[DataComparison] Found Dexie database: ${dexieDb.name}`);

      // Open the IndexedDB database directly
      const dbRequest = indexedDB.open(dexieDb.name);

      return new Promise((resolve) => {
        dbRequest.onerror = () => {
          console.warn(`[DataComparison] Failed to open Dexie database ${dexieDb.name}`);
          resolve({ count: 0, error: `Failed to open database: ${dbRequest.error?.message}` });
        };

        dbRequest.onsuccess = () => {
          const db = dbRequest.result;

          try {
            // Look for the collection table (Dexie typically uses the collection name as table name)
            const objectStoreNames = Array.from(db.objectStoreNames);
            console.log(`[DataComparison] Available object stores in ${dexieDb.name}:`, objectStoreNames);

            // Try to find the collection table
            let tableName = objectStoreNames.find(name =>
              name === collectionName ||
              name.toLowerCase() === collectionName.toLowerCase() ||
              name.includes(collectionName)
            );

            // If not found, try the first non-system table
            if (!tableName && objectStoreNames.length > 0) {
              tableName = objectStoreNames.find(name => !name.startsWith('_'));
            }

            if (!tableName) {
              console.log(`[DataComparison] No suitable table found in Dexie database ${dexieDb.name}`);
              db.close();
              resolve({ count: 0, error: null });
              return;
            }

            console.log(`[DataComparison] Using table: ${tableName}`);

            // Start a transaction to count documents
            const transaction = db.transaction([tableName], 'readonly');
            const objectStore = transaction.objectStore(tableName);
            const countRequest = objectStore.count();

            countRequest.onsuccess = () => {
              const count = countRequest.result;
              console.log(`[DataComparison] Dexie count for ${collectionName}: ${count}`);
              db.close();
              resolve({ count, error: null });
            };

            countRequest.onerror = () => {
              console.warn(`[DataComparison] Failed to count documents in ${tableName}`);
              db.close();
              resolve({ count: 0, error: `Failed to count documents: ${countRequest.error?.message}` });
            };

          } catch (error) {
            console.warn(`[DataComparison] Error accessing Dexie database ${dexieDb.name}:`, error);
            db.close();
            resolve({ count: 0, error: error.message });
          }
        };
      });

    } catch (error) {
      console.warn(`[DataComparison] Dexie count error for ${collectionName}:`, error.message);
      return { count: 0, error: error.message };
    }
  };

  /**
   * Get documents that exist in Dexie but not in LAN
   */
  const getDexieOnlyDocuments = async (collectionName) => {
    try {
      console.log(`[DataComparison] Finding documents in Dexie but not in LAN for ${collectionName}`);

      // Get all documents from Dexie
      const dexieDocs = await getAllDexieDocuments(collectionName);
      if (dexieDocs.length === 0) {
        return { documents: [], error: null };
      }

      // Get all document IDs from LAN
      const lanDocIds = await getLanDocumentIds(collectionName);

      // Find documents that exist in Dexie but not in LAN
      const dexieOnlyDocs = dexieDocs.filter(doc => !lanDocIds.includes(doc._id || doc.id));

      console.log(`[DataComparison] Found ${dexieOnlyDocs.length} documents in Dexie but not in LAN for ${collectionName}`);

      return { documents: dexieOnlyDocs, error: null };

    } catch (error) {
      console.error(`[DataComparison] Error finding Dexie-only documents for ${collectionName}:`, error);
      return { documents: [], error: error.message };
    }
  };

  /**
   * Get all documents from Dexie IndexedDB
   */
  const getAllDexieDocuments = async (collectionName) => {
    try {
      const databasePrefix = localStorage.getItem('LOCAL_STORAGE_DATABASE_PREFIX') || '';
      const dexieDbName = `${databasePrefix}${collectionName}`;

      const databases = await indexedDB.databases();
      const dexieDb = databases.find(db =>
        db.name === dexieDbName ||
        db.name === collectionName ||
        db.name.includes(collectionName)
      );

      if (!dexieDb) {
        return [];
      }

      const dbRequest = indexedDB.open(dexieDb.name);

      return new Promise((resolve) => {
        dbRequest.onerror = () => resolve([]);

        dbRequest.onsuccess = () => {
          const db = dbRequest.result;

          try {
            const objectStoreNames = Array.from(db.objectStoreNames);
            let tableName = objectStoreNames.find(name =>
              name === collectionName ||
              name.toLowerCase() === collectionName.toLowerCase() ||
              name.includes(collectionName)
            );

            if (!tableName && objectStoreNames.length > 0) {
              tableName = objectStoreNames.find(name => !name.startsWith('_'));
            }

            if (!tableName) {
              db.close();
              resolve([]);
              return;
            }

            const transaction = db.transaction([tableName], 'readonly');
            const objectStore = transaction.objectStore(tableName);
            const getAllRequest = objectStore.getAll();

            getAllRequest.onsuccess = () => {
              const documents = getAllRequest.result || [];
              db.close();
              resolve(documents);
            };

            getAllRequest.onerror = () => {
              db.close();
              resolve([]);
            };

          } catch (error) {
            db.close();
            resolve([]);
          }
        };
      });

    } catch (error) {
      console.warn(`[DataComparison] Error getting all Dexie documents for ${collectionName}:`, error);
      return [];
    }
  };

  /**
   * Get all document IDs from LAN CouchDB
   */
  const getLanDocumentIds = async (collectionName) => {
    try {
      const lanCredentialsHash = localStorage.getItem('LAN_CREDENTIALS');
      const databasePrefix = localStorage.getItem('LOCAL_STORAGE_DATABASE_PREFIX');

      if (!lanCredentialsHash || !databasePrefix) {
        return [];
      }

      const decryptPassword = (password) =>
        Base64.decode(
          Base64.decode(Base64.decode(Base64.decode(Base64.decode(password))))
        );

      const lanCredentials = JSON.parse(decryptPassword(lanCredentialsHash));
      const { host, port, username, password } = lanCredentials;

      if (!host || !port || !username || !password) {
        return [];
      }

      const dbUrl = `http://${host}:${port}/${databasePrefix}${collectionName}`;
      const allDocsUrl = `${dbUrl}/_all_docs`;

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      // Create authorization header
      const authHeader = 'Basic ' + btoa(`${username}:${password}`);

      const response = await fetch(allDocsUrl, {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Accept': 'application/json',
          'Authorization': authHeader
        }
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        return [];
      }

      const result = await response.json();
      return result.rows
        .filter(row => !row.id.startsWith('_design/') && !row.id.startsWith('_local/'))
        .map(row => row.id);

    } catch (error) {
      console.warn(`[DataComparison] Error getting LAN document IDs for ${collectionName}:`, error);
      return [];
    }
  };

  /**
   * Get document count from Remote CouchDB
   */
  const getRemoteDocumentCount = async (collectionName) => {
    try {
      // Get database prefix
      const databasePrefix = localStorage.getItem('LOCAL_STORAGE_DATABASE_PREFIX');
      if (!databasePrefix) {
        return { count: 0, error: 'Database prefix not configured' };
      }

      // Use the correct remote connection string
      const REMOTE_CONNECTION = 'https://therick:<EMAIL>/';
      const dbUrl = `${REMOTE_CONNECTION}${databasePrefix}${collectionName}`;
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000);

      // First try to get database info
      const response = await fetch(dbUrl, {
        method: 'GET',
        signal: controller.signal,
        headers: { 'Accept': 'application/json' }
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        if (response.status === 404) {
          return { count: 0, error: null }; // Database doesn't exist, which is valid
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const dbInfo = await response.json();

      // Get actual document count excluding design documents
      const allDocsUrl = `${dbUrl}/_all_docs?limit=0&startkey=""&endkey="_design/"`;
      const docsController = new AbortController();
      const docsTimeoutId = setTimeout(() => docsController.abort(), 15000);

      const docsResponse = await fetch(allDocsUrl, {
        method: 'GET',
        signal: docsController.signal,
        headers: { 'Accept': 'application/json' }
      });

      clearTimeout(docsTimeoutId);

      if (docsResponse.ok) {
        const docsResult = await docsResponse.json();
        return { count: docsResult.total_rows || 0, error: null };
      } else {
        // Fallback to database info doc_count (may include design docs)
        const actualCount = Math.max(0, (dbInfo.doc_count || 0) - (dbInfo.doc_del_count || 0));
        return { count: actualCount, error: null };
      }

    } catch (error) {
      console.warn(`[DataComparison] Remote count error for ${collectionName}:`, error.message);
      return { count: 0, error: error.message };
    }
  };

  /**
   * Test database connectivity before running comparison
   */
  const testDatabaseConnectivity = async () => {
    console.log('[DataComparison] Testing database connectivity...');

    if (!pouchDatabase || typeof pouchDatabase !== 'function') {
      throw new Error('pouchDatabase factory function not available');
    }

    if (!databasePrefix) {
      console.warn('[DataComparison] No database prefix provided');
    }

    // Test with a known collection (organizations is usually present)
    const testCollection = 'organizations';
    try {
      const testDb = pouchDatabase(testCollection, databasePrefix);
      console.log(`[DataComparison] Test database instance created for ${testCollection}:`, {
        hasDb: !!testDb,
        hasPouchDb: !!testDb?.pouchDb,
        hasDb: !!testDb?.db,
        hasDexieDb: !!testDb?.dexieDb,
        name: testDb?.name,
        hasLanString: !!testDb?.lanString,
        hasDatabasePrefix: !!testDb?.databasePrefix
      });

      return testDb;
    } catch (error) {
      console.error('[DataComparison] Database connectivity test failed:', error);
      throw error;
    }
  };

  /**
   * Run data comparison for all collections
   * ENHANCED: Now includes connectivity testing and better error handling
   */
  const runDataComparison = async () => {
    setComparisonLoading(true);
    console.log('[DataComparison] Starting data comparison across all collections');

    try {
      // ENHANCED: Test database connectivity first
      await testDatabaseConnectivity();
      console.log('[DataComparison] Database connectivity test passed');

      // IMPROVED: Get collections from modules prop - much more accurate!
      let collectionsToProcess = [];
      try {
        collectionsToProcess = getCollectionsFromModules();

        if (collectionsToProcess.length === 0) {
          console.warn('[DataComparison] No collections found in modules, using fallback');
          collectionsToProcess = getAllDatabaseCollections();
        } else {
          // Optional: Verify collections have data (can be disabled for speed)
          console.log('[DataComparison] Optionally verifying collections have data...');
          collectionsToProcess = await verifyCollectionsHaveData(collectionsToProcess);
        }
      } catch (error) {
        console.error('[DataComparison] Error getting collections from modules, using fallback:', error);
        collectionsToProcess = getAllDatabaseCollections();
      }

      const comparisons = [];
      console.log(`[DataComparison] Processing ${collectionsToProcess.length} collections from app modules`);

      // Process collections in smaller batches for better performance
      const batchSize = 3; // Reduced batch size for more responsive UI
      for (let i = 0; i < collectionsToProcess.length; i += batchSize) {
        const batch = collectionsToProcess.slice(i, i + batchSize);
        console.log(`[DataComparison] Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(collectionsToProcess.length / batchSize)}: ${batch.join(', ')}`);

        const batchPromises = batch.map(collection => compareCollectionData(collection));
        const batchResults = await Promise.allSettled(batchPromises);

        batchResults.forEach((result, index) => {
          if (result.status === 'fulfilled') {
            comparisons.push(result.value);
          } else {
            console.error(`[DataComparison] Failed to compare ${batch[index]}:`, result.reason);
            comparisons.push({
              collection: batch[index],
              dexieCount: 0,
              pouchCount: 0,
              lanCount: 0,
              remoteCount: 0,
              difference: 0,
              syncStatus: 'error',
              dexieError: result.reason?.message || 'Unknown error',
              pouchError: null,
              lanError: null,
              remoteError: null,
              lastSyncTime: null,
              lanAvailable: false,
              remoteAvailable: false
            });
          }
        });

        // Small delay between batches to prevent overwhelming the system
        if (i + batchSize < collectionsToProcess.length) {
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      }

      setComparisonData(comparisons);
      setLastComparisonTime(new Date().toLocaleString());

      // ENHANCED: Better result analysis
      const successfulComparisons = comparisons.filter(c => c.syncStatus !== 'error');
      const totalDifferences = successfulComparisons.reduce((sum, comp) => sum + comp.difference, 0);
      const syncedCollections = successfulComparisons.filter(c => c.syncStatus === 'synced').length;
      const outOfSyncCollections = successfulComparisons.filter(c => c.syncStatus === 'out-of-sync').length;
      const errorCollections = comparisons.filter(c => c.syncStatus === 'error').length;

      console.log(`[DataComparison] Comparison complete:`, {
        total: comparisons.length,
        synced: syncedCollections,
        outOfSync: outOfSyncCollections,
        errors: errorCollections,
        totalDifferences
      });

      if (errorCollections === comparisons.length) {
        message.error('All database comparisons failed. Check console for details.');
      } else if (totalDifferences === 0 && errorCollections === 0) {
        message.success(`All ${successfulComparisons.length} databases are in sync!`);
      } else {
        message.warning(`Found ${totalDifferences} differences across ${outOfSyncCollections} collections. ${errorCollections} collections had errors.`);
      }

    } catch (error) {
      console.error('[DataComparison] Error running comparison:', error);
      message.error(`Comparison failed: ${error.message}`);
    } finally {
      setComparisonLoading(false);
    }
  };

  /**
   * Render sync progress section
   */
  const renderSyncProgress = () => {
    if (!syncStatus.isRunning && !syncStatus.currentStage) {
      return (
        <Alert
          message="No sync in progress"
          description="Click 'Force Full Sync' to start synchronizing all databases"
          type="info"
          showIcon
        />
      );
    }

    return (
      <Space direction="vertical" style={{ width: '100%' }}>
        <Progress
          percent={Math.round(syncStatus.progress)}
          status={syncStatus.isRunning ? 'active' : 'success'}
          strokeColor={{
            '0%': '#108ee9',
            '100%': '#87d068',
          }}
        />

        <Row gutter={16}>
          <Col span={8}>
            <Statistic
              title="Current Stage"
              value={syncStatus.currentStage || 'Idle'}
              prefix={<DatabaseOutlined />}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="Progress"
              value={`${syncStatus.progress.toFixed(1)}%`}
              prefix={<SyncOutlined spin={syncStatus.isRunning} />}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="Duration"
              value={formatDuration(syncStatus.startTime ? Date.now() - syncStatus.startTime : 0)}
              prefix={<ClockCircleOutlined />}
            />
          </Col>
        </Row>

        {syncStatus.errors.length > 0 && (
          <Alert
            message={`${syncStatus.errors.length} Error(s) Occurred`}
            description={
              <ul style={{ margin: 0, paddingLeft: 20 }}>
                {syncStatus.errors.slice(0, 3).map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
                {syncStatus.errors.length > 3 && (
                  <li>... and {syncStatus.errors.length - 3} more</li>
                )}
              </ul>
            }
            type="warning"
            showIcon
          />
        )}
      </Space>
    );
  };

  /**
   * Render sync history
   */
  const renderSyncHistory = () => {
    if (syncHistory.length === 0) {
      return (
        <Alert
          message="No sync history"
          description="Sync history will appear here after running synchronization"
          type="info"
          showIcon
        />
      );
    }

    return (
      <List
        dataSource={syncHistory}
        renderItem={(item) => (
          <List.Item>
            <List.Item.Meta
              avatar={
                item.status === 'Completed' ? (
                  <CheckCircleOutlined style={{ color: '#52c41a' }} />
                ) : item.status === 'Failed' ? (
                  <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
                ) : (
                  <ClockCircleOutlined style={{ color: '#faad14' }} />
                )
              }
              title={
                <Space>
                  <Text strong>{item.type}</Text>
                  <Tag color={getStatusColor(item.status)}>{item.status}</Tag>
                  {item.errorCount > 0 && (
                    <Tag color="warning">{item.errorCount} errors</Tag>
                  )}
                </Space>
              }
              description={
                <Space>
                  <Text type="secondary">{item.timestamp}</Text>
                  <Text type="secondary">Duration: {formatDuration(item.duration)}</Text>
                </Space>
              }
            />
          </List.Item>
        )}
      />
    );
  };

  /**
   * Render data comparison table
   */
  const renderDataComparison = () => {
    const columns = [
      {
        title: 'Collection',
        dataIndex: 'collection',
        key: 'collection',
        sorter: (a, b) => a.collection.localeCompare(b.collection),
        render: (text) => <Text strong>{text}</Text>
      },
      {
        title: 'Dexie Count',
        dataIndex: 'dexieCount',
        key: 'dexieCount',
        sorter: (a, b) => a.dexieCount - b.dexieCount,
        render: (count, record) => (
          <Space>
            <Text strong={count > 0} style={{ color: count > 0 ? '#1890ff' : undefined }}>
              {count}
            </Text>
            {count > 0 && (
              <Tooltip title="Documents found in Dexie - click 'Dexie Only' to compare with LAN">
                <InfoCircleOutlined style={{ color: '#1890ff' }} />
              </Tooltip>
            )}
            {record.dexieError && (
              <Tooltip title={record.dexieError}>
                <WarningOutlined style={{ color: '#ff4d4f' }} />
              </Tooltip>
            )}
          </Space>
        )
      },

      {
        title: 'LAN CouchDB',
        dataIndex: 'lanCount',
        key: 'lanCount',
        sorter: (a, b) => a.lanCount - b.lanCount,
        render: (count, record) => {
          return (
            <Space>
              <Text strong>{count}</Text>
              {record.lanError && (
                <Tooltip title={record.lanError}>
                  <WarningOutlined style={{ color: '#ff4d4f' }} />
                </Tooltip>
              )}
              {!record.lanError && record.lanAvailable && (
                <Tooltip title="LAN server reachable">
                  <CheckCircleOutlined style={{ color: '#52c41a', fontSize: '12px' }} />
                </Tooltip>
              )}
              {!record.lanAvailable && (
                <Tooltip title="LAN server not reachable or not configured">
                  <ExclamationCircleOutlined style={{ color: '#faad14', fontSize: '12px' }} />
                </Tooltip>
              )}
            </Space>
          );
        }
      },

      {
        title: 'Difference',
        dataIndex: 'difference',
        key: 'difference',
        sorter: (a, b) => a.difference - b.difference,
        render: (diff) => (
          <Badge
            count={diff}
            style={{
              backgroundColor: diff === 0 ? '#52c41a' : '#ff4d4f'
            }}
          />
        )
      },
      {
        title: 'Sync Status',
        dataIndex: 'syncStatus',
        key: 'syncStatus',
        filters: [
          { text: 'Synced', value: 'synced' },
          { text: 'Out of Sync', value: 'out-of-sync' },
          { text: 'Error', value: 'error' }
        ],
        onFilter: (value, record) => record.syncStatus === value,
        render: (status) => {
          const config = {
            'synced': { color: 'success', icon: <CheckCircleOutlined /> },
            'out-of-sync': { color: 'warning', icon: <WarningOutlined /> },
            'error': { color: 'error', icon: <ExclamationCircleOutlined /> }
          };
          const { color, icon } = config[status] || config['error'];

          return (
            <Tag color={color} icon={icon}>
              {status.replace('-', ' ').toUpperCase()}
            </Tag>
          );
        }
      },
      {
        title: 'Last Sync',
        dataIndex: 'lastSyncTime',
        key: 'lastSyncTime',
        render: (time) => (
          <Text type="secondary">
            {time ? new Date(time).toLocaleString() : 'Never'}
          </Text>
        )
      },
      {
        title: 'Actions',
        key: 'actions',
        width: 200,
        render: (_, record) => {
          const isLoading = syncingCollections.has(record.collection);
          const isDexieLoading = loadingDexieOnly.has(record.collection);
          const isDisabled = syncingAll || comparisonLoading;

          return (
            <Space size="small">
              <Button
                type="primary"
                size="small"
                icon={<SyncOutlined spin={isLoading} />}
                onClick={() => handleCollectionSync(record.collection)}
                loading={isLoading}
                disabled={isDisabled}
                title={`Sync ${record.collection} collection`}
              >
                Sync
              </Button>
              <Button
                size="small"
                icon={<SearchOutlined spin={isDexieLoading} />}
                onClick={() => handleFindDexieOnlyDocs(record.collection)}
                loading={isDexieLoading}
                disabled={isDisabled}
                title={`Find documents in Dexie but not in LAN for ${record.collection}`}
                style={{
                  backgroundColor: record.dexieCount > 0 ? '#1890ff' : undefined,
                  borderColor: record.dexieCount > 0 ? '#1890ff' : undefined,
                  color: record.dexieCount > 0 ? 'white' : undefined
                }}
              >
                Dexie Only
              </Button>
            </Space>
          );
        }
      }
    ];

    return (
      <div>
        <Space style={{ marginBottom: 16 }} wrap>
          <Button
            type="primary"
            icon={<DiffOutlined />}
            onClick={runDataComparison}
            loading={comparisonLoading}
            disabled={syncingAll}
          >
            Run Comparison
          </Button>
          <Button
            type="primary"
            icon={<SyncOutlined spin={syncingAll} />}
            onClick={handleSyncAll}
            loading={syncingAll}
            disabled={comparisonLoading || comparisonData.length === 0}
            style={{ backgroundColor: '#52c41a', borderColor: '#52c41a' }}
          >
            Sync All
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => setComparisonData([])}
            disabled={comparisonLoading || syncingAll}
          >
            Clear Results
          </Button>
          <Button
            icon={<SearchOutlined />}
            onClick={() => {
              console.log('[DatabaseSync] DEBUG: Modules prop:', modules);
              const collections = getCollectionsFromModules();
              console.log('[DatabaseSync] DEBUG: Collections from modules:', collections);
              message.info(`Found ${collections.length} collections. Check console for details.`);
            }}
            disabled={comparisonLoading || syncingAll}
          >
            Debug Collections
          </Button>
          {lastComparisonTime && (
            <Text type="secondary">
              Last comparison: {lastComparisonTime}
            </Text>
          )}
        </Space>

        {comparisonLoading && (
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <Spin size="large" />
            <div style={{ marginTop: 8 }}>
              <Text>Comparing database collections...</Text>
            </div>
          </div>
        )}

        {comparisonData.length > 0 && !comparisonLoading && (
          <>
            <Alert
              message={syncingAll ? 'Syncing All Collections...' : 'Comparison Results'}
              description={
                <Space wrap>
                  <Text>
                    Total Collections: {comparisonData.length}
                  </Text>
                  <Text>
                    In Sync: {comparisonData.filter(d => d.syncStatus === 'synced').length}
                  </Text>
                  <Text>
                    Out of Sync: {comparisonData.filter(d => d.syncStatus === 'out-of-sync').length}
                  </Text>
                  <Text>
                    Errors: {comparisonData.filter(d => d.syncStatus === 'error').length}
                  </Text>
                  {syncingCollections.size > 0 && (
                    <Text type="warning">
                      Currently Syncing: {Array.from(syncingCollections).join(', ')}
                    </Text>
                  )}
                  {syncingAll && (
                    <Text type="warning">
                      Bulk sync in progress...
                    </Text>
                  )}
                </Space>
              }
              type={
                syncingAll || syncingCollections.size > 0
                  ? 'info'
                  : comparisonData.every(d => d.syncStatus === 'synced')
                    ? 'success'
                    : 'warning'
              }
              style={{ marginBottom: 16 }}
              showIcon
            />

            <Table
              columns={columns}
              dataSource={comparisonData}
              rowKey="collection"
              pagination={{
                pageSize: 20,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `${range[0]}-${range[1]} of ${total} collections`
              }}
              size="small"
            />
          </>
        )}

        {comparisonData.length === 0 && !comparisonLoading && (
          <Alert
            message="No comparison data"
            description="Click 'Run Comparison' to analyze differences between Dexie (old local database) and LAN CouchDB"
            type="info"
            showIcon
          />
        )}

        {/* Modal for displaying Dexie-only documents */}
        <Modal
          title={`Documents in Dexie but not in LAN - ${selectedCollection}`}
          open={showDexieModal}
          onCancel={() => setShowDexieModal(false)}
          width={800}
          footer={[
            <Button key="close" onClick={() => setShowDexieModal(false)}>
              Close
            </Button>
          ]}
        >
          {selectedCollection && dexieOnlyDocs[selectedCollection] && (
            <div>
              <Alert
                message={`Found ${dexieOnlyDocs[selectedCollection].length} documents`}
                description={`These documents exist in Dexie but are missing from the LAN database for collection: ${selectedCollection}`}
                type={dexieOnlyDocs[selectedCollection].length > 0 ? 'warning' : 'info'}
                style={{ marginBottom: 16 }}
                showIcon
              />

              {dexieOnlyDocs[selectedCollection].length > 0 && (
                <Table
                  dataSource={dexieOnlyDocs[selectedCollection]}
                  rowKey={(record) => record._id || record.id || Math.random()}
                  pagination={{ pageSize: 10 }}
                  size="small"
                  scroll={{ x: true }}
                  columns={[
                    {
                      title: 'Document ID',
                      dataIndex: '_id',
                      key: '_id',
                      render: (id, record) => id || record.id || 'No ID',
                      width: 200
                    },
                    {
                      title: 'Created At',
                      dataIndex: 'createdAt',
                      key: 'createdAt',
                      render: (date) => date ? new Date(date).toLocaleString() : 'N/A',
                      width: 150
                    },
                    {
                      title: 'Updated At',
                      dataIndex: 'updatedAt',
                      key: 'updatedAt',
                      render: (date) => date ? new Date(date).toLocaleString() : 'N/A',
                      width: 150
                    },
                    {
                      title: 'Data Preview',
                      key: 'preview',
                      render: (_, record) => {
                        const preview = JSON.stringify(record, null, 2);
                        const truncated = preview.length > 100 ? preview.substring(0, 100) + '...' : preview;
                        return (
                          <Tooltip title={<pre style={{ whiteSpace: 'pre-wrap' }}>{preview}</pre>}>
                            <Text code style={{ fontSize: '11px' }}>
                              {truncated}
                            </Text>
                          </Tooltip>
                        );
                      }
                    }
                  ]}
                />
              )}
            </div>
          )}
        </Modal>
      </div>
    );
  };

  return (
    <div style={{ padding: '24px' }}>
      <Title level={3}>
        <DatabaseOutlined /> Database Synchronization
      </Title>

      <Paragraph>
        Compare data between Dexie (old local database) and LAN CouchDB to identify missing documents.
        Use the "Dexie Only" button to find documents that exist in old Dexie databases but are missing from LAN,
        then manually sync individual collections as needed.
      </Paragraph>

      <Tabs defaultActiveKey="comparison" type="card">
        <TabPane
          tab={
            <span>
              <DiffOutlined />
              Data Comparison & Sync
            </span>
          }
          key="comparison"
        >
          <Card
            title="Dexie vs LAN Database Comparison"
            extra={
              <Space>
                <InfoCircleOutlined />
                <Text type="secondary">Compare Dexie (old local) vs LAN CouchDB and find missing documents</Text>
              </Space>
            }
          >
            {renderDataComparison()}
          </Card>
        </TabPane>

        <TabPane
          tab={
            <span>
              <SyncOutlined />
              Sync History
            </span>
          }
          key="history"
        >
          <Card title="Sync History">
            {renderSyncHistory()}
          </Card>
        </TabPane>
      </Tabs>


    </div>
  );
};

export default DatabaseSync;
