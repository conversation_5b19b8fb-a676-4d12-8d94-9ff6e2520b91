import ProCard from '@ant-design/pro-card'
import {
  Avatar,
  List,
  Typography,
  Row,
  Col,
  Space,
  Card,
  Tag,
  Empty,
  Image
} from 'antd'
import {
  BankOutlined,
  EnvironmentOutlined,
  MailOutlined,
  PhoneOutlined,
  MobileOutlined,
  UserOutlined,
  TeamOutlined,
  InfoCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons'
import { useEffect, useState } from 'react'

const { Text, Title } = Typography;

console.log('🏢 Fetching organization data from:')

const OrganizationInformation = ({pouchDatabase,databasePrefix}) => {

    const [organisation,setOrganisation]=useState(null)
    const [loading,setLoading]=useState(true)
    const [error,setError]=useState(null)
    const [logoBlob,setLogoBlob]=useState(null)
    const [logoUrl,setLogoUrl]=useState(null)

    useEffect(()=>{
        const fetchOrganisation = async () => {
            try {
                setLoading(true)
                setError(null)

                //get company id data from databaseprefix
                const companyId = databasePrefix.split('_')[1]

                console.log('🏢 Fetching organization data from:', companyId)
                const organisationDB = pouchDatabase('organizations', databasePrefix)
                const res = await organisationDB.get(companyId.toUpperCase())
                console.log('🏢 Organization data received:', res)

                if (res) {
                    setOrganisation(res)
                    console.log('🏢 Organization set:', res)

                    // Try to fetch logo if organization has attachments
                    if (res._attachments && res._attachments.logo) {
                        try {
                            console.log('🖼️ Fetching organization logo...')
                            const logoAttachment = await organisationDB.getAttachment(res._id, 'logo')
                            setLogoBlob(logoAttachment)
                            console.log('🖼️ Logo loaded successfully')
                        } catch (logoError) {
                            console.warn('🖼️ Could not load organization logo:', logoError)
                            // Don't set error for logo failure, just continue without logo
                        }
                    } else {
                        console.log('🖼️ No logo attachment found for organization')
                    }
                } else {
                    console.log('🏢 No organization data found')
                    setError('No organization data found')
                }
            } catch (err) {
                console.error('🏢 Error fetching organization:', err)
                setError(err.message)
            } finally {
                setLoading(false)
            }
        }

        fetchOrganisation()
    },[pouchDatabase, databasePrefix])

    // Create and cleanup blob URL when logoBlob changes
    useEffect(() => {
        if (logoBlob && logoBlob instanceof Blob) {
            try {
                const url = URL.createObjectURL(logoBlob)
                setLogoUrl(url)

                // Cleanup function
                return () => {
                    URL.revokeObjectURL(url)
                    setLogoUrl(null)
                }
            } catch (error) {
                console.error('🖼️ Error creating object URL for logo:', error)
                setLogoUrl(null)
            }
        } else {
            setLogoUrl(null)
        }
    }, [logoBlob])

  // Helper function to get organization initials
  const getOrganizationInitials = (name) => {
    if (!name) return 'ORG';
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // Helper component for contact information items
  const ContactInfoItem = ({ icon, label, value, copyable = false }) => {
    if (!value) return null;

    return (
      <Col xs={24} sm={12} md={8}>
        <Space align="start" style={{ width: '100%' }}>
          <div style={{
            color: '#1890ff',
            fontSize: '16px',
            marginTop: '2px',
            minWidth: '20px'
          }}>
            {icon}
          </div>
          <div style={{ flex: 1 }}>
            <Text type="secondary" style={{ fontSize: '12px', display: 'block' }}>
              {label}
            </Text>
            <Text strong copyable={copyable} style={{ fontSize: '14px' }}>
              {value}
            </Text>
          </div>
        </Space>
      </Col>
    );
  };

  return (
    <ProCard
      title={
        <Space>
          <BankOutlined style={{ color: '#1890ff' }} />
          Organization Information
        </Space>
      }
      loading={loading}
      style={{
        background: 'linear-gradient(145deg, #ffffff 0%, #f9f9f9 100%)',
        borderRadius: '12px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.08)'
      }}
    >
      {error && (
        <Card
          style={{
            background: '#fff2f0',
            border: '1px solid #ffccc7',
            borderRadius: '8px',
            textAlign: 'center'
          }}
        >
          <Space direction="vertical" size="middle">
            <ExclamationCircleOutlined
              style={{ fontSize: '32px', color: '#ff4d4f' }}
            />
            <div>
              <Text type="danger" style={{ fontSize: '16px', fontWeight: 500 }}>
                {error === 'No organization data found'
                  ? 'Organization Information Not Found'
                  : 'Error Loading Organization Data'
                }
              </Text>
              <br />
              <Text type="secondary" style={{ fontSize: '14px' }}>
                {error === 'No organization data found'
                  ? 'Please add organization details in the settings to display company information.'
                  : `Unable to load organization data: ${error}`
                }
              </Text>
            </div>
            {process.env.NODE_ENV === 'development' && (
              <Card size="small" style={{ background: '#f5f5f5', border: 'none' }}>
                <Text type="secondary" style={{ fontSize: '11px' }}>
                  <InfoCircleOutlined /> Debug: Check console for details. Database: {databasePrefix}
                </Text>
              </Card>
            )}
          </Space>
        </Card>
      )}

      {organisation && (
        <Row gutter={[24, 24]}>
          {/* Organization Header */}
          <Col span={24}>
            <Card
              style={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                border: 'none',
                borderRadius: '12px',
                color: 'white'
              }}
            >
              <Row gutter={[16, 16]} align="middle">
                <Col xs={24} sm={6} style={{ textAlign: 'center' }}>
                  {logoUrl ? (
                    <div style={{
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      padding: '8px',
                      background: 'white',
                      borderRadius: '12px',
                      border: '2px solid rgba(255, 255, 255, 0.2)'
                    }}>
                      <Image
                        src={logoUrl}
                        alt={`${organisation.name} logo`}
                        style={{
                          maxWidth: '80px',
                          maxHeight: '80px',
                          objectFit: 'contain',
                          borderRadius: '8px'
                        }}
                        preview={{
                          mask: 'View Logo'
                        }}
                      />
                    </div>
                  ) : (
                    <Avatar
                      size={80}
                      style={{
                        backgroundColor: 'rgba(255, 255, 255, 0.2)',
                        color: 'white',
                        fontSize: '28px',
                        fontWeight: 'bold',
                        border: '3px solid rgba(255, 255, 255, 0.3)'
                      }}
                    >
                      {getOrganizationInitials(organisation.name)}
                    </Avatar>
                  )}
                </Col>
                <Col xs={24} sm={18}>
                  <Title level={2} style={{ color: 'white', margin: 0, marginBottom: '8px' }}>
                    {organisation.name || 'Organization Name'}
                  </Title>
                  <Space wrap>
                    <Tag color="rgba(255, 255, 255, 0.2)" style={{ color: 'white', border: '1px solid rgba(255, 255, 255, 0.3)' }}>
                      <BankOutlined /> Company
                    </Tag>
                    {organisation._id && (
                      <Tag color="rgba(255, 255, 255, 0.2)" style={{ color: 'white', border: '1px solid rgba(255, 255, 255, 0.3)' }}>
                        ID: {organisation._id}
                      </Tag>
                    )}
                  </Space>
                </Col>
              </Row>
            </Card>
          </Col>

          {/* Contact Information */}
          <Col span={24}>
            <Card
              title={
                <Space>
                  <InfoCircleOutlined style={{ color: '#1890ff' }} />
                  Contact Information
                </Space>
              }
              style={{
                borderRadius: '8px',
                boxShadow: '0 1px 4px rgba(0,0,0,0.05)'
              }}
            >
              <Row gutter={[16, 16]}>
                <ContactInfoItem
                  icon={<EnvironmentOutlined />}
                  label="Address"
                  value={organisation.address}
                />
                <ContactInfoItem
                  icon={<MailOutlined />}
                  label="Email"
                  value={organisation.email}
                  copyable={true}
                />
                <ContactInfoItem
                  icon={<PhoneOutlined />}
                  label="Phone"
                  value={organisation.phone}
                  copyable={true}
                />
                {organisation.alternative_phone && (
                  <ContactInfoItem
                    icon={<MobileOutlined />}
                    label="Alternative Phone"
                    value={organisation.alternative_phone}
                    copyable={true}
                  />
                )}
              </Row>
            </Card>
          </Col>

          {/* Contact Persons */}
          {organisation.contacts && organisation.contacts.length > 0 && (
            <Col span={24}>
              <Card
                title={
                  <Space>
                    <TeamOutlined style={{ color: '#1890ff' }} />
                    Contact Persons
                    <Tag color="blue">{organisation.contacts.length}</Tag>
                  </Space>
                }
                style={{
                  borderRadius: '8px',
                  boxShadow: '0 1px 4px rgba(0,0,0,0.05)'
                }}
              >
                <List
                  itemLayout="horizontal"
                  dataSource={organisation.contacts}
                  renderItem={(item, index) => (
                    <List.Item
                      style={{
                        padding: '16px',
                        background: index % 2 === 0 ? '#fafafa' : 'white',
                        borderRadius: '8px',
                        marginBottom: '8px'
                      }}
                    >
                      <List.Item.Meta
                        avatar={
                          <Avatar
                            size={48}
                            style={{
                              backgroundColor: '#1890ff',
                              fontSize: '18px'
                            }}
                          >
                            {`${item.first_name?.[0] || ''}${item.last_name?.[0] || ''}`.toUpperCase() || <UserOutlined />}
                          </Avatar>
                        }
                        title={
                          <Text strong style={{ fontSize: '16px' }}>
                            {`${item.first_name || ''} ${item.last_name || ''}`.trim() || 'Contact Person'}
                          </Text>
                        }
                        description={
                          <Space direction="vertical" size="small" style={{ width: '100%' }}>
                            {item.email && (
                              <Space>
                                <MailOutlined style={{ color: '#1890ff' }} />
                                <Text copyable style={{ fontSize: '13px' }}>
                                  {item.email}
                                </Text>
                              </Space>
                            )}
                            {item.phone && (
                              <Space>
                                <PhoneOutlined style={{ color: '#52c41a' }} />
                                <Text copyable style={{ fontSize: '13px' }}>
                                  {item.phone}
                                </Text>
                              </Space>
                            )}
                            {item.mobile && (
                              <Space>
                                <MobileOutlined style={{ color: '#722ed1' }} />
                                <Text copyable style={{ fontSize: '13px' }}>
                                  {item.mobile}
                                </Text>
                              </Space>
                            )}
                          </Space>
                        }
                      />
                    </List.Item>
                  )}
                />
              </Card>
            </Col>
          )}
        </Row>
      )}

      {!loading && !error && !organisation && (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={
            <Space direction="vertical" size="middle">
              <Text type="secondary" style={{ fontSize: '16px' }}>
                No Organization Information Available
              </Text>
              <Text type="secondary" style={{ fontSize: '14px' }}>
                Please add organization details in the settings to display company information.
              </Text>
              <Card size="small" style={{ background: '#f0f2f5', border: 'none', borderRadius: '6px' }}>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  <InfoCircleOutlined /> Go to Settings → Organization to add your company information
                </Text>
              </Card>
            </Space>
          }
        />
      )}
    </ProCard>
  )
}

export default OrganizationInformation